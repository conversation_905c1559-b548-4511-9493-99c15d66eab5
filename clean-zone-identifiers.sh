#!/bin/bash

# Script pour nettoyer les fichiers Zone.Identifier
# Ces fichiers sont créés par Windows pour marquer les fichiers téléchargés

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧹 Nettoyage des fichiers Zone.Identifier"
echo "========================================"
echo ""

# Compter les fichiers Zone.Identifier
zone_files=$(find . -name "*:Zone.Identifier" -type f | wc -l)

if [ "$zone_files" -eq 0 ]; then
    log_success "Aucun fichier Zone.Identifier trouvé !"
    exit 0
fi

log_warning "Trouvé $zone_files fichiers Zone.Identifier"
echo ""

# Afficher les fichiers trouvés
log_info "Fichiers à supprimer :"
find . -name "*:Zone.Identifier" -type f | head -10
if [ "$zone_files" -gt 10 ]; then
    echo "... et $(($zone_files - 10)) autres fichiers"
fi
echo ""

# Demander confirmation
read -p "Voulez-vous supprimer tous ces fichiers ? (y/N) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Suppression des fichiers Zone.Identifier..."
    
    # Supprimer tous les fichiers Zone.Identifier
    find . -name "*:Zone.Identifier" -type f -delete
    
    log_success "Tous les fichiers Zone.Identifier ont été supprimés !"
    
    # Vérifier qu'il n'en reste plus
    remaining=$(find . -name "*:Zone.Identifier" -type f | wc -l)
    if [ "$remaining" -eq 0 ]; then
        log_success "Nettoyage terminé avec succès !"
    else
        log_warning "$remaining fichiers Zone.Identifier restants"
    fi
else
    log_info "Nettoyage annulé"
fi

echo ""
echo "💡 Pour éviter ce problème à l'avenir :"
echo "   - Utilisez git clone au lieu de télécharger des archives ZIP"
echo "   - Ou utilisez WSL/Linux pour le développement"
echo "   - Ou désactivez les Zone.Identifier dans Windows"
