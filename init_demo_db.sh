#!/bin/bash

echo "🚀 Initialisation de la base de données demo_pos avec Point of Sale..."

# Arrêter le conteneur Odoo s'il tourne
echo "📦 Arrêt du conteneur Odoo..."
docker stop odoo16_app 2>/dev/null || true

# Supprimer la base de données existante
echo "🗑️ Suppression de la base de données existante..."
docker exec odoo16_db psql -U odoo -d postgres -c "DROP DATABASE IF EXISTS demo_pos;" 2>/dev/null || true

# Créer la base de données avec les modules
echo "🔧 Création de la base de données avec Point of Sale et données de démonstration..."
docker run --rm \
    --network odoo_odoo-network \
    -v /home/<USER>/git/odoo/addons:/mnt/extra-addons \
    -v /home/<USER>/git/odoo/config:/etc/odoo \
    odoo:16.0 \
    odoo -d demo_pos \
    --init=base,point_of_sale \
    --without-demo=False \
    --stop-after-init \
    --db_host=odoo16_db \
    --db_user=odoo \
    --db_password=odoo \
    --addons-path=/usr/lib/python3/dist-packages/odoo/addons,/var/lib/odoo/addons/16.0,/mnt/extra-addons

if [ $? -eq 0 ]; then
    echo "✅ Base de données créée avec succès !"
    
    # Redémarrer le conteneur Odoo
    echo "🔄 Redémarrage du conteneur Odoo..."
    docker start odoo16_app
    
    echo "🎉 Configuration terminée !"
    echo "📧 Vous pouvez maintenant vous connecter avec :"
    echo "   URL: http://localhost:8069"
    echo "   Base de données: demo_pos"
    echo "   Login: admin"
    echo "   Password: admin"
else
    echo "❌ Erreur lors de la création de la base de données"
    exit 1
fi
