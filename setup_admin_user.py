#!/usr/bin/env python3
import psycopg2
import hashlib
import base64
import os

def hash_password(password):
    """Hash password using <PERSON><PERSON><PERSON>'s method"""
    return hashlib.pbkdf2_hmac('sha1', password.encode('utf-8'), b'', 1)

def setup_admin_user():
    """Setup admin user with custom credentials"""
    
    # Database connection
    conn = psycopg2.connect(
        host="localhost",
        port="5432", 
        database="demo_pos",
        user="odoo",
        password="odoo"
    )
    
    cursor = conn.cursor()
    
    try:
        # Hash the password
        password_hash = base64.b64encode(hash_password("admin")).decode('ascii')
        
        # Update admin user
        cursor.execute("""
            UPDATE res_users 
            SET login = %s, 
                password = %s,
                email = %s
            WHERE id = 2
        """, ("<EMAIL>", password_hash, "<EMAIL>"))
        
        # Update partner information
        cursor.execute("""
            UPDATE res_partner 
            SET name = %s,
                email = %s
            WHERE id = (SELECT partner_id FROM res_users WHERE id = 2)
        """, ("Administrator", "<EMAIL>"))
        
        conn.commit()
        print("✅ Admin user configured successfully!")
        print("📧 Email: <EMAIL>")
        print("🔑 Password: admin")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    setup_admin_user()
